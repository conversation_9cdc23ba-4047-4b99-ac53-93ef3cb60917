package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.QuanYiApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

/**
 * EccOuterChannelController 测试类
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
@AutoConfigureWebMvc
public class EccOuterChannelControllerTest {

    @Autowired
    private MockMvc mockMvc;

    /**
     * 测试更新渠道状态接口
     */
    @Test
    public void testUpdateStatus() throws Exception {
        log.info("=== 开始测试更新渠道状态接口 ===");
        
        // 测试参数
        Long channelId = 1L;
        Integer status = 0; // 启用状态
        
        // 发送GET请求
        mockMvc.perform(MockMvcRequestBuilders.get("/a/ecc/outerChannel/updateStatus")
                        .param("id", channelId.toString())
                        .param("status", status.toString()))
                .andDo(print()) // 打印请求和响应详情
                .andExpect(MockMvcResultMatchers.status().isOk()); // 期望HTTP状态码为200
        
        log.info("✅ 更新渠道状态接口测试完成");
    }

    /**
     * 测试参数验证 - 缺少必需参数
     */
    @Test
    public void testUpdateStatusWithMissingParams() throws Exception {
        log.info("=== 开始测试参数验证 ===");
        
        // 测试缺少id参数
        mockMvc.perform(MockMvcRequestBuilders.get("/a/ecc/outerChannel/updateStatus")
                        .param("status", "0"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest()); // 期望400错误
        
        // 测试缺少status参数
        mockMvc.perform(MockMvcRequestBuilders.get("/a/ecc/outerChannel/updateStatus")
                        .param("id", "1"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest()); // 期望400错误
        
        log.info("✅ 参数验证测试完成");
    }

    /**
     * 测试无效的状态值
     */
    @Test
    public void testUpdateStatusWithInvalidStatus() throws Exception {
        log.info("=== 开始测试无效状态值 ===");
        
        // 测试无效的状态值
        mockMvc.perform(MockMvcRequestBuilders.get("/a/ecc/outerChannel/updateStatus")
                        .param("id", "1")
                        .param("status", "2")) // 无效状态值
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk()) // HTTP状态正常
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false)); // 业务失败
        
        log.info("✅ 无效状态值测试完成");
    }
}
