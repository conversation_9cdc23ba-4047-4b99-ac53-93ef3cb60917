<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccOuterChannelDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO">
    <!--@mbg.generated-->
    <!--@Table ecc_outer_channel-->
    <id column="outer_channel_id" jdbcType="BIGINT" property="outerChannelId" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="api_key" jdbcType="VARCHAR" property="apiKey" />
    <result column="api_secret" jdbcType="VARCHAR" property="apiSecret" />
    <result column="is_disabled" jdbcType="INTEGER" property="isDisabled" />
    <result column="ip_whitelist" jdbcType="VARCHAR" property="ipWhitelist" />
    <result column="zop_referrer_code" jdbcType="VARCHAR" property="zopReferrerCode" />
    <result column="parent_channel_id" jdbcType="BIGINT" property="parentChannelId" />
    <result column="channel_level" jdbcType="TINYINT" property="channelLevel" />
    <result column="deduction_rate" jdbcType="TINYINT" property="deductionRate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>

  <!-- 包含父渠道名称的ResultMap -->
  <resultMap id="WithParentResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO" extends="BaseResultMap">
    <result column="parentChannelName" jdbcType="VARCHAR" property="parentChannelName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    outer_channel_id, channel_name, api_key, api_secret, is_disabled, ip_whitelist, zop_referrer_code,
    parent_channel_id, channel_level, deduction_rate, create_time, update_time,
    deleted
  </sql>

  <sql id="Left_Join_Base_Column_List">
          <!--@mbg.generated-->
    eoc.outer_channel_id,eoc.channel_name,eoc.api_key,eoc.api_secret,eoc.is_disabled,eoc.ip_whitelist,eoc.zop_referrer_code,
    eoc.parent_channel_id,eoc.channel_level,eoc.deduction_rate,eoc.create_time,eoc.update_time,eoc.deleted
  </sql>

  <!-- 使用递归CTE查询渠道的所有上级渠道链 -->
  <!-- 最多 10层 防止无限递归-->
  <select id="selectParentChannelChain" resultMap="BaseResultMap">
    WITH RECURSIVE parent_chain AS (
    SELECT
    <include refid="Left_Join_Base_Column_List"/>
    ,
      1 as level
    FROM ecc_outer_channel eoc
           INNER JOIN ecc_outer_channel c ON c.parent_channel_id = eoc.outer_channel_id
    WHERE c.outer_channel_id = #{channelId}
      AND eoc.deleted = 0

    UNION ALL

    SELECT
    <include refid="Left_Join_Base_Column_List"/>
    ,
      pc.level + 1
    FROM ecc_outer_channel eoc
           INNER JOIN parent_chain pc ON pc.parent_channel_id = eoc.outer_channel_id
    WHERE eoc.deleted = 0
      AND pc.level &lt; 10
    )
    SELECT
    <include refid="Base_Column_List"/>
    FROM parent_chain
    ORDER BY level ASC
  </select>

  <!-- 使用递归CTE查询渠道的所有下级渠道 -->
  <!-- 防止无限递归，最多10层-->
  <select id="selectAllDescendantChannels" resultMap="BaseResultMap">
    WITH RECURSIVE descendant_tree AS (
    SELECT
    <include refid="Base_Column_List"/>
    ,
      1 as level
    FROM ecc_outer_channel
    WHERE parent_channel_id = #{parentChannelId}
      AND deleted = 0

    UNION ALL

    SELECT
    <include refid="Left_Join_Base_Column_List"/>
    ,
      dt.level + 1
    FROM ecc_outer_channel eoc
           INNER JOIN descendant_tree dt ON dt.outer_channel_id = eoc.parent_channel_id
    WHERE eoc.deleted = 0
      AND dt.level &lt; 10
    )
    SELECT
    <include refid="Base_Column_List"/>
    FROM descendant_tree
    ORDER BY level ASC, outer_channel_id ASC
  </select>

  <!-- 分页查询外部渠道（包含父渠道名称） -->
  <select id="selectPageWithParent" resultMap="WithParentResultMap">
    SELECT
      t.outer_channel_id,
      t.channel_name,
      t.api_key,
      t.api_secret,
      t.ip_whitelist,
      t.zop_referrer_code,
      t.parent_channel_id,
      t.channel_level,
      t.deduction_rate,
      t.is_disabled,
      t.deleted,
      t.create_time,
      t.update_time,
      t1.channel_name AS parentChannelName
    FROM ecc_outer_channel t
    LEFT JOIN ecc_outer_channel t1 ON (t1.outer_channel_id = t.parent_channel_id AND t1.deleted = 0)
    WHERE t.deleted = 0
    <if test="req.outerChannelId != null">
      AND t.outer_channel_id = #{req.outerChannelId}
    </if>
    <if test="req.channelName != null and req.channelName != ''">
      AND t.channel_name LIKE CONCAT('%', #{req.channelName}, '%')
    </if>
    <if test="req.apiKey != null and req.apiKey != ''">
      AND t.api_key = #{req.apiKey}
    </if>
    <if test="req.parentChannelId != null">
      AND t.parent_channel_id = #{req.parentChannelId}
    </if>
    <if test="req.channelLevel != null">
      AND t.channel_level = #{req.channelLevel}
    </if>
    <!-- 权限控制 -->
    <if test="req.outChannelsLimit != null">
      <choose>
        <when test="req.outChannelsLimit.size() > 0">
          AND t.parent_channel_id IN
          <foreach collection="req.outChannelsLimit" item="channelId" open="(" separator="," close=")">
            #{channelId}
          </foreach>
        </when>
        <otherwise>
          <!-- 无权限，返回空结果 -->
          AND t.outer_channel_id = -1
        </otherwise>
      </choose>
    </if>
    ORDER BY t.outer_channel_id DESC
  </select>
</mapper>