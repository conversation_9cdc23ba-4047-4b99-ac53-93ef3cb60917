package com.yuelan.hermes.quanyi.common.util;


import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * <AUTHOR> 2025/8/22
 * @since 2025/8/22
 */
public class WebContextUtil {
    /**
     * 模拟 Web 上下文（供手动调用）
     */
    public static void mockWebContext() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        ServletRequestAttributes attributes = new ServletRequestAttributes(request, response);
        RequestContextHolder.setRequestAttributes(attributes);
    }

    /**
     * 清理 Web 上下文（供手动调用）
     */
    public static void clearWebContext() {
        RequestContextHolder.resetRequestAttributes();
    }

    /**
     * 在模拟的 Web 上下文中执行任务（自动清理）
     *
     * @param task 要执行的任务（Lambda 表达式）
     */
    public static void runWithMockWebContext(Runnable task) {
        try {
            mockWebContext(); // 1. 模拟 Web 上下文
            task.run();       // 2. 执行任务
        } finally {
            clearWebContext(); // 3. 自动清理
        }
    }

    /**
     * 在模拟的 Web 上下文中执行任务（带返回值，自动清理）
     *
     * @param supplier 要执行的任务（Lambda 表达式）
     * @return 任务执行结果
     */
    public static <T> T runWithMockWebContext(java.util.function.Supplier<T> supplier) {
        try {
            mockWebContext(); // 1. 模拟 Web 上下文
            return supplier.get(); // 2. 执行任务并返回结果
        } finally {
            clearWebContext(); // 3. 自动清理
        }
    }
}