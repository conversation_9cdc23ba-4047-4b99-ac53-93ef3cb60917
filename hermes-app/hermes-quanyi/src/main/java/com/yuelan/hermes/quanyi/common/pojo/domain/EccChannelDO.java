package com.yuelan.hermes.quanyi.common.pojo.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.yuelan.result.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR> 2024/5/4 上午1:07
 * 电商卡推广渠道
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "ecc_channel")
public class EccChannelDO extends BaseDO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "channel_id", type = IdType.AUTO)
    private Long channelId;

    /**
     * 渠道名字
     */
    @TableField(value = "channel_name")
    private String channelName;


    @TableField(value = "channel_level")
    private Integer channelLevel;

    @TableField(value = "parent_channel_id")
    private Long parentChannelId;

    @TableLogic
    private Integer deleted;
}