package com.yuelan.hermes.quanyi.biz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccChannelDO;
import com.yuelan.hermes.quanyi.common.util.MpPageUtil;
import com.yuelan.hermes.quanyi.controller.request.EccChannelListReq;
import com.yuelan.hermes.quanyi.controller.request.EccChannelSaveReq;
import com.yuelan.hermes.quanyi.controller.response.EccChannelResp;
import com.yuelan.hermes.quanyi.mapper.EccChannelDOMapper;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/5/2 上午12:03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EccChannelDOService extends ServiceImpl<EccChannelDOMapper, EccChannelDO> {

    private final EccChannelDOMapper eccChannelDOMapper;
    private final EccAdminChannelDOService adminChannelDOService;

    /**
     * 电商卡渠道分页列表
     *
     * @param req 分页和查询参数
     * @return 分页数据
     */
    public PageData<EccChannelResp> page(EccChannelListReq req) {
        IPage<EccChannelDO> page = MpPageUtil.convertPageRequest(req);
        page = eccChannelDOMapper.selectPage(page, req.buildQueryWrapper());
        List<EccChannelResp> respList = page.getRecords().stream()
                .map(EccChannelResp::buildResp).collect(Collectors.toList());
        return PageData.create(respList, page.getTotal(), req.getPage(), req.getSize());
    }

    /**
     * 电商卡渠道保存
     *
     * @param req 保存参数
     */
    public void save(EccChannelSaveReq req) {
        req.setChannelId(null);
        EccChannelDO dbChannelDO = eccChannelDOMapper.selectByChannelName(req.getChannelName());
        if (dbChannelDO != null) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "渠道名称已存在");
        }
        EccChannelDO eccChannelDO = req.convert();
        save(eccChannelDO);
    }


    public void updateById(EccChannelSaveReq req) {
        EccChannelDO dbChannelDO = getById(req.getChannelId());
        if (dbChannelDO == null) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "渠道不存在");
        }
        EccChannelDO eccChannelDO = req.convert();
        if (!Objects.equals(eccChannelDO.getChannelName(), dbChannelDO.getChannelName())) {
            EccChannelDO dbChannelByName = eccChannelDOMapper.selectByChannelName(eccChannelDO.getChannelName());
            if (dbChannelByName != null) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "渠道名称已存在");
            }
        }
        this.updateById(eccChannelDO);
    }


    public List<EccChannelResp> selectOptionsByAdmin() {
        List<Long> channelIds = adminChannelDOService.listAllInnerChannelsLimit();
        List<EccChannelDO> dbChannelDOs = this.list();
        dbChannelDOs.sort(Comparator.comparing(EccChannelDO::getChannelId).reversed());
        List<EccChannelResp> respList = new ArrayList<>();
        if (Objects.isNull(channelIds)) {
            // 没有限制
            for (EccChannelDO dbChannelDO : dbChannelDOs) {
                respList.add(EccChannelResp.buildResp(dbChannelDO));
            }
        } else {
            for (EccChannelDO dbChannelDO : dbChannelDOs) {
                if (channelIds.contains(dbChannelDO.getChannelId())) {
                    respList.add(EccChannelResp.buildResp(dbChannelDO));
                }
            }
        }
        return respList;
    }

    /**
     * 返回渠道限制 返回null表示不限制渠道
     */
    public List<Long> selectLimitChannelIds() {
        return adminChannelDOService.listAllInnerChannelsLimit();
    }

    public List<Long> getAllDescendantChannelIds(Long innerParentChannelId) {
        if (innerParentChannelId == null) {
            return new ArrayList<>();
        }

        // 使用递归CTE一次性查询所有下级渠道
        List<EccChannelDO> descendants = eccChannelDOMapper.selectAllDescendantChannels(innerParentChannelId);
        return descendants.stream()
                .map(EccChannelDO::getChannelId)
                .collect(Collectors.toList());
    }

}
