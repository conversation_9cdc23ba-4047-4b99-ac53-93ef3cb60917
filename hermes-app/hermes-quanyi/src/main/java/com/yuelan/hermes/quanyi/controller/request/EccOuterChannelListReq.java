package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR> 2024/5/17 下午4:05
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class EccOuterChannelListReq extends PageRequest {

    @Schema(description = "外部渠道id")
    public Long outerChannelId;

    @Schema(description = "渠道名字")
    private String channelName;

    @Schema(description = "对接参数apiKey")
    private String apiKey;

    @Schema(description = "上级渠道ID")
    private Long parentChannelId;

    @Schema(description = "渠道层级类型：1-一级渠道，2-二级渠道")
    private Integer channelLevel;

    /**
     * 权限限制字段，由Service层设置
     */
    private List<Long> outChannelsLimit;

}
