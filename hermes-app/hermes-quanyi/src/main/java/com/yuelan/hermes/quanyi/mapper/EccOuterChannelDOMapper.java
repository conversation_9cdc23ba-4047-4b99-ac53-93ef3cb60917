package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.hermes.quanyi.controller.request.EccOuterChannelListReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2024/5/17 下午4:02
 */
public interface EccOuterChannelDOMapper extends MPJBaseMapper<EccOuterChannelDO> {
    /**
     * 根据名字查询渠道
     */
    default EccOuterChannelDO selectByName(String channelName) {
        return selectOne(Wrappers.<EccOuterChannelDO>lambdaQuery()
                .eq(EccOuterChannelDO::getChannelName, channelName));
    }

    /**
     * 根据父级渠道ID查询直接子渠道
     */
    default List<EccOuterChannelDO> selectByParentChannelId(Long parentChannelId) {
        return selectList(Wrappers.<EccOuterChannelDO>lambdaQuery()
                .eq(EccOuterChannelDO::getParentChannelId, parentChannelId));
    }

    /**
     * 使用递归CTE一次性查询渠道的所有上级渠道链
     *
     * @param channelId 渠道ID
     * @return 上级渠道链列表，按层级从低到高排序（不包含当前渠道）
     */
    List<EccOuterChannelDO> selectParentChannelChain(@Param("channelId") Long channelId);

    /**
     * 使用递归CTE一次性查询渠道的所有下级渠道
     *
     * @param parentChannelId 父渠道ID
     * @return 所有下级渠道列表
     */
    List<EccOuterChannelDO> selectAllDescendantChannels(@Param("parentChannelId") Long parentChannelId);

    /**
     * 分页查询外部渠道（包含父渠道名称）
     *
     * @param page 分页参数
     * @param req  查询条件
     * @return 分页结果
     */
    IPage<EccOuterChannelDO> selectPageWithParent(IPage<EccOuterChannelDO> page, @Param("req") EccOuterChannelListReq req);
}