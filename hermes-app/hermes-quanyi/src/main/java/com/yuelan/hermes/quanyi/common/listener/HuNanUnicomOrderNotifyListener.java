package com.yuelan.hermes.quanyi.common.listener;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.yuelan.hermes.quanyi.biz.service.HttpAsyncTaskService;
import com.yuelan.hermes.quanyi.common.enums.HNUnicomWoPayPkgEnum;
import com.yuelan.hermes.quanyi.common.enums.SuccessStrategyEnum;
import com.yuelan.hermes.quanyi.common.event.HuNanUnicomOrderBatchNotifyEvent;
import com.yuelan.hermes.quanyi.common.event.HuNanUnicomOrderNotifyEvent;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.config.task.HttpTaskRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class HuNanUnicomOrderNotifyListener {

    public static final String BUSINESS_TYPE = "HU_NAN_ORDER_API_CALLBACK";

    public static final String CALLBACK_URL = "https://www.wanyaokeji.com/admin-api/business/dtdh/wyd/orderCallbackMethod";

    private final HttpAsyncTaskService httpAsyncTaskService;

    @EventListener(HuNanUnicomOrderNotifyEvent.class)
    public void apiCallBack(HuNanUnicomOrderNotifyEvent event) {
        BenefitOrderDO orderDO = event.getOrderDO();
        if (Objects.isNull(orderDO) || !orderDO.getPayStatus().equals(1)) {
            return;
        }
        if (!HNUnicomWoPayPkgEnum.hn_ct.getPkgId().equals(orderDO.getPayChannelPkgId())) {
            return;
        }
        log.info("{}，回调给北京电通:{}", HNUnicomWoPayPkgEnum.hn_ct.getName(), JSON.toJSONString(event));
        HttpTaskRequest request = this.eventRequest(CallbackBO.build(event.getOrderDO()), 6);
        httpAsyncTaskService.createTask(request);
    }

    @EventListener(HuNanUnicomOrderBatchNotifyEvent.class)
    public void batchCallBack(HuNanUnicomOrderBatchNotifyEvent event) {
        List<BenefitOrderDO> orderList = event.getOrderList();
        if (orderList.isEmpty()) {
            return;
        }
        List<HttpTaskRequest> requestList = orderList.stream()
                .filter(order -> order.getPayStatus().equals(1)
                        && HNUnicomWoPayPkgEnum.hn_ct.getPkgId().equals(order.getPayChannelPkgId()))
                .map(CallbackBO::build)
                .map(order -> this.eventRequest(order, 1))
                .collect(Collectors.toList());
        if (!requestList.isEmpty()) {
            httpAsyncTaskService.createTask(requestList);
        }
    }

    private HttpTaskRequest eventRequest(CallbackBO callbackBO, Integer maxRetryCount) {
        return HttpTaskRequest.builder()
                .url(CALLBACK_URL)
                .method("POST")
                .body(JSON.toJSONString(callbackBO))
                .headers(null)
                .sourceSystem("HU_NAN_ORDER")
                // 与回调注册的业务类型对应
                .businessType(BUSINESS_TYPE)
                .businessId(callbackBO.getOrderCode())
                .maxRetryCount(maxRetryCount)
                .retryInterval(300)
                // code == 0 为成功
                .successStrategy(SuccessStrategyEnum.RESPONSE_JSON_CODE_0)
                .build();
    }

    @Data
    public static class CallbackBO {

        private String orderTime;

        private String mobile;

        private String orderCode;

        private String productId;

        private String productIdProvince;

        private String productIdCB;

        private String productCodeJD;

        private String from;

        private String province;

        private String orderStatus;

        private String message;

        private String productName;

        private String price;

        private String infoType;

        public static CallbackBO build(BenefitOrderDO orderDO) {
            CallbackBO callbackBO = new CallbackBO();
            String orderTime = DateUtil.format(orderDO.getCreateTime(), "yyyy-MM-dd HH:mm:ss");
            callbackBO.setOrderTime(orderTime);
            callbackBO.setMobile(orderDO.getPhone());
            callbackBO.setOrderCode(orderDO.getOrderNo());
            callbackBO.setProductId("9487");
            callbackBO.setProductIdProvince("91341031");
            callbackBO.setFrom("杭州道盟");
            callbackBO.setProvince("湖南");
            callbackBO.setOrderStatus("0");
            callbackBO.setProductName(orderDO.getProdName());
            callbackBO.setPrice(orderDO.getOrderAmount().toString());
            callbackBO.setInfoType(orderDO.getPackageName());
            return callbackBO;
        }

    }

}
