// package com.yuelan.hermes.quanyi.controller.response;
//
// import io.swagger.v3.oas.annotations.media.Schema;
// import lombok.Data;
//
// /**
//  * 渠道用户信息响应
//  *
//  * <AUTHOR>
//  * @since 2025-01-22
//  */
// @Data
// @Schema(description = "渠道用户信息响应")
// public class ChannelUserInfoResp {
//
//     /**
//      * 管理员ID
//      */
//     @Schema(description = "管理员ID")
//     private Long adminId;
//
//     /**
//      * 用户名
//      */
//     @Schema(description = "用户名")
//     private String username;
//
//     /**
//      * 昵称
//      */
//     @Schema(description = "昵称")
//     private String nickname;
//
//     /**
//      * 手机号
//      */
//     @Schema(description = "手机号")
//     private String phone;
//
//     /**
//      * 邮箱
//      */
//     @Schema(description = "邮箱")
//     private String email;
//
//     /**
//      * 关联的外部渠道ID
//      */
//     @Schema(description = "关联的外部渠道ID")
//     private Long outerChannelId;
//
//     /**
//      * 渠道名称
//      */
//     @Schema(description = "渠道名称")
//     private String channelName;
//
//     /**
//      * 渠道层级深度
//      */
//     @Schema(description = "渠道层级深度")
//     private Integer channelDepth;
//
//     /**
//      * 上级渠道ID
//      */
//     @Schema(description = "上级渠道ID")
//     private Long parentChannelId;
//
//     /**
//      * 扣量比例
//      */
//     @Schema(description = "扣量比例（0-100，单位：%）")
//     private Integer deductionRate;
//
//     /**
//      * 是否禁用
//      */
//     @Schema(description = "是否禁用：0-正常，1-禁用")
//     private Integer isDisabled;
//
//     /**
//      * 是否可以管理下级渠道
//      */
//     @Schema(description = "是否可以管理下级渠道")
//     private Boolean canManageSubChannels;
//
//     /**
//      * 是否可以设置扣量比例
//      */
//     @Schema(description = "是否可以设置扣量比例")
//     private Boolean canSetDeductionRate;
// }
