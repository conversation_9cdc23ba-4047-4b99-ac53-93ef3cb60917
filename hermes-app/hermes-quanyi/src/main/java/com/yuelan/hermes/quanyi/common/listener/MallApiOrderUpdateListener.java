package com.yuelan.hermes.quanyi.common.listener;

import cn.hutool.core.util.StrUtil;
import com.yuelan.hermes.quanyi.biz.service.EccOuterChannelDOService;
import com.yuelan.hermes.quanyi.biz.service.HttpAsyncTaskService;
import com.yuelan.hermes.quanyi.common.enums.SuccessStrategyEnum;
import com.yuelan.hermes.quanyi.common.enums.ZopOrderSource;
import com.yuelan.hermes.quanyi.common.event.MallApiOrderBatchUpdateEvent;
import com.yuelan.hermes.quanyi.common.event.MallApiOrderUpdateEvent;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO;
import com.yuelan.hermes.quanyi.config.task.HttpTaskRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2025/3/24
 * @since 2025/3/24
 */
@Slf4j
@Component
@AllArgsConstructor
public class MallApiOrderUpdateListener {
    public static final String BUSINESS_TYPE = "ECC_MALL_API_CALLBACK";

    private final HttpAsyncTaskService httpAsyncTaskService;
    private final EccOuterChannelDOService eccOuterChannelDOService;

    /**
     * 回调给渠道调用方
     *
     * @see com.yuelan.hermes.quanyi.common.enums.ZopOrderSource ZOP_MALL_API 网上商城API订单
     */
    @EventListener(MallApiOrderUpdateEvent.class)
    public void apiCallBack(MallApiOrderUpdateEvent event) {
        // 只有网上商城API订单 才会发送这个版本回调
        if (!ZopOrderSource.ZOP_MALL_API.getCode()
                .equals(event.getOldOrder().getZopOrderSource())) {
            return;
        }
        if (StrUtil.isEmpty(event.getOldOrder().getCallbackUrl())) {
            return;
        }
        if (!statusChange(event)) {
            return;
        }
        log.info("MallApiOrderUpdateEvent-回调给渠道:{}", event);

        HttpTaskRequest request = event2Request(event);
        httpAsyncTaskService.createTask(request);
    }

    /**
     * 批量加入回调任务
     */
    @EventListener(MallApiOrderBatchUpdateEvent.class)
    public void batchCallBack(MallApiOrderBatchUpdateEvent event) {
        delListEvent(event);
    }

    private void delListEvent(MallApiOrderBatchUpdateEvent events) {
        List<HttpTaskRequest> requests = new ArrayList<>();
        for (MallApiOrderUpdateEvent event : events.getEvents()) {
            // 只有网上商城API订单 才会发送这个版本回调
            if (!ZopOrderSource.ZOP_MALL_API.getCode()
                    .equals(event.getOldOrder().getZopOrderSource())) {
                continue;
            }
            if (StrUtil.isEmpty(event.getOldOrder().getCallbackUrl())) {
                continue;
            }
            if (!statusChange(event)) {
                continue;
            }
            requests.add(event2Request(event));
        }
        httpAsyncTaskService.createTask(requests);
    }

    private HttpTaskRequest event2Request(MallApiOrderUpdateEvent event) {
        return HttpTaskRequest.builder()
                .url(event.getOldOrder().getCallbackUrl())
                .method("POST")
                .body(eccOuterChannelDOService.buildSignBody(event.getUpdatedOrder()))
                .headers(null)
                .sourceSystem("ECC_MALL_API")
                // 与回调注册的业务类型对应
                .businessType(BUSINESS_TYPE)
                .businessId(String.valueOf(event.getOldOrder().getZopOrderId()))
                .maxRetryCount(6)
                .retryInterval(300)
                // code == 0 为成功
                .successStrategy(SuccessStrategyEnum.RESPONSE_JSON_CODE_0)
                .build();
    }


    // public String buildSignBody(EccZopOrderDO updatedOrder) {
    //     EccQueryOrderApiResp req = EccQueryOrderApiResp.buildResp(updatedOrder);
    //     JSONObject bodyReq = JSON.parseObject(JSON.toJSONString(req));
    //     String signStr = bodyReq.entrySet().stream().sorted(Map.Entry.comparingByKey())
    //             .map(entry -> entry.getKey() + "=" + entry.getValue())
    //             .collect(Collectors.joining("&"));
    //     Long channelId = updatedOrder.getChannelId();
    //     EccOuterChannelDO channelDO = eccOuterChannelDOService.getById(channelId);
    //     if (Objects.isNull(channelDO)) {
    //         log.error("外部渠道回调通知异常 outerChannelId:{}, orderId:{}", channelId, updatedOrder.getZopOrderId());
    //         throw BizException.create(BizErrorCodeEnum.MCH_NOT_FOUND, "外部渠道回调通知异常");
    //     }
    //     String sign = DigestUtils.md5Hex(signStr + "&secret=" + channelDO.getApiSecret());
    //     bodyReq.put("sign", sign);
    //     return JSON.toJSONString(bodyReq);
    // }

    private boolean statusChange(MallApiOrderUpdateEvent event) {
        EccZopOrderDO oldOrder = event.getOldOrder();
        EccZopOrderDO updatedOrder = event.getUpdatedOrder();
        // 订单状态变化
        if (!Objects.equals(oldOrder.getOrderSyncStatus(), updatedOrder.getOrderSyncStatus())) {
            return true;
        }
        // 错误描述变化
        if (!Objects.equals(oldOrder.getOrderSyncResp(), updatedOrder.getOrderSyncResp())) {
            return true;
        }
        // 联通状态变化
        if (!Objects.equals(oldOrder.getZopOrderState(), updatedOrder.getZopOrderState())) {
            return true;
        }
        return false;
    }


}
