package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.result.enums.YesOrNoEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/5/17 下午4:05
 */
@Data
public class EccOuterChannelSaveReq {

    @NotNull(message = "外部渠道ID不能为空", groups = {EditGroup.class})
    @Schema(description = "外部渠道id")
    private Long outerChannelId;


    @NotEmpty(message = "渠道名字不能为空", groups = {AddGroup.class})
    @Schema(description = "渠道名字")
    private String channelName;

    @Schema(title = "是否禁用", description = "是否禁用：0-表示正常，1-表示禁用")
    private Integer isDisabled;


    @Schema(title = "服务器白名单", description = "多个ip用英文逗号隔开")
    private String ipWhitelist;

    /**
     * 联通zop发展人编号
     */
    @Schema(description = "联通zop发展人编号")
    private String zopReferrerCode;

    /**
     * 上级渠道ID（用于建立渠道层级关系）
     */
    @Schema(description = "上级渠道ID，一级去渠道请传 0")
    private Long parentChannelId;

    /**
     * 扣量比例（0-100，单位：%）
     */
    @Schema(description = "扣量比例（0-100，单位：%）")
    @NotNull(message = "扣量比例，不扣量请传 0")
    private Integer deductionRate;

    /**
     * 关联的管理员账号ID
     */
    @Schema(description = "关联的管理员账号ID")
    private Long adminId;

    /**
     * 渠道层级类型：1-一级渠道，2-二级渠道
     */
    @Schema(description = "渠道层级类型：1-一级渠道，2-二级渠道")
    private Integer channelLevel;

    public EccOuterChannelDO convert() {
        EccOuterChannelDO channelDO = new EccOuterChannelDO();
        channelDO.setOuterChannelId(outerChannelId);
        channelDO.setChannelName(channelName);
        YesOrNoEnum yesOrNoEnum = YesOrNoEnum.of(isDisabled);
        if (yesOrNoEnum != null) {
            channelDO.setIsDisabled(isDisabled);
        }
        channelDO.setZopReferrerCode(zopReferrerCode);
        channelDO.setIpWhitelist(ipWhitelist);
        channelDO.setParentChannelId(parentChannelId);
        channelDO.setDeductionRate(deductionRate);
        channelDO.setChannelLevel(channelLevel);
        return channelDO;
    }

}
