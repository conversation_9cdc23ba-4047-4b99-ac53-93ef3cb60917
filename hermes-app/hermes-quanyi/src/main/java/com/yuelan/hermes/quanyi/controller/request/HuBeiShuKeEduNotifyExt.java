package com.yuelan.hermes.quanyi.controller.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 湖北数科教育包回调通知扩展信息
 *
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Data
public class HuBeiShuKeEduNotifyExt {

    /**
     * 订单号，销售品侧的订单号，唯一值
     */
    private String order_id;

    /**
     * 订购类型 0:订购 1:退订
     */
    private Integer on_type;

    /**
     * 产品id
     */
    private String product_id;

    /**
     * tw值 类似用户唯一标识或者订单标识
     */
    private String client_order_id;

    /**
     * 如果失败，返回失败详细描述，可供渠道后台排查失败原因
     */
    private String desc;

    /**
     * 渠道id
     */
    private String app_id;

    /**
     * 用户识别码
     */
    private String identification_code;

    /**
     * 判断是否为订购操作
     */
    @JSONField(serialize = false)
    public boolean isSubscribe() {
        return on_type != null && on_type == 0;
    }

    /**
     * 判断是否为退订操作
     */
    @JSONField(serialize = false)
    public boolean isUnsubscribe() {
        return on_type != null && on_type == 1;
    }


}
