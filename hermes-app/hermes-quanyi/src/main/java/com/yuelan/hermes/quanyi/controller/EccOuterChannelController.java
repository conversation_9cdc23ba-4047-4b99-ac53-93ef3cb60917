package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.biz.service.EccOuterChannelDOService;
import com.yuelan.hermes.quanyi.controller.request.EccOuterChannelListReq;
import com.yuelan.hermes.quanyi.controller.request.EccOuterChannelSaveReq;
import com.yuelan.hermes.quanyi.controller.response.EccOuterChannelResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 外部渠道管理控制器（包含层级管理功能）
 * <AUTHOR> 2024/5/17 下午4:03
 */
@Slf4j
@Validated
@RestController
@Tag(name = "号卡/后台api/外部渠道管理")
@RequiredArgsConstructor
@RequestMapping("/a/ecc/outerChannel")
public class EccOuterChannelController {

    private final EccOuterChannelDOService eccOuterChannelDOService;

    /**
     * 新增外部渠道
     */
    @Log(title = "新增号卡外部渠道", type = OperationType.INSERT)
    @Operation(summary = "新增号卡外部渠道")
    @PostMapping("/add")
    public BizResult<Void> add(@RequestBody @Validated(AddGroup.class) EccOuterChannelSaveReq req) {
        eccOuterChannelDOService.save(req);
        return BizResult.ok();
    }

    /**
     * 更新外部渠道
     */
    @Log(title = "更新号卡外部渠道", type = OperationType.UPDATE)
    @Operation(summary = "更新号卡外部渠道")
    @PostMapping("/edit")
    public BizResult<Void> edit(@RequestBody @Validated(EditGroup.class) EccOuterChannelSaveReq req) {
        eccOuterChannelDOService.update(req);
        return BizResult.ok();
    }

    /**
     * 号卡外部渠道分页列表
     */
    @Operation(summary = "号卡外部渠道分页列表")
    @PostMapping("/page")
    public BizResult<PageData<EccOuterChannelResp>> page(@RequestBody EccOuterChannelListReq req) {
        return BizResult.create(eccOuterChannelDOService.pageList(req));
    }


    @Operation(summary = "号卡外部渠道选择项")
    @PostMapping("/selectOptions")
    public BizResult<List<EccOuterChannelResp>> selectOptions() {
        return BizResult.create(eccOuterChannelDOService.selectOptionsByAdmin());
    }
}
