<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.yuelan</groupId>
        <artifactId>hermes-app</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>hermes-quanyi</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>虚拟权益</description>

    <dependencies>
        <!--springboottest-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <!-- 工具包 -->
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>hermes-commons</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>yuelan-commons-boot</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>yuelan-plugins-redisson</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>yuelan-plugins-doc</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>yuelan-plugins-log</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>yuelan-plugins-token</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>yuelan-plugins-oss</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>yuelan-plugins-mp</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <!-- spring -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!--DB -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.yulichang</groupId>
            <artifactId>mybatis-plus-join-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>tech.powerjob</groupId>
            <artifactId>powerjob-worker-spring-boot-starter</artifactId>
        </dependency>
        <!--rocketmq -->
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
        </dependency>
        <!-- mail -->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
        </dependency>
        <!--zop jar包 -->
        <dependency>
            <groupId>com.chinaunicom</groupId>
            <artifactId>zop-sdk</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/zop-sdk-1.0.0.jar
            </systemPath><!-- 项目 src/main/resources/lib 目录下的 zop-sdk-1.0.0.jar -->
        </dependency>
        <!--zop jar 需要-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.78</version>
        </dependency>
        <!-- alibabaTop平台 sdk如果后台重新申请权限需要重新生成sdk  -->
        <dependency>
            <groupId>com.taobao</groupId>
            <artifactId>alibaba-top-sdk</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/taobao-sdk-java-auto_1596019530202-20240506.jar
            </systemPath>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.biezhi</groupId>
            <artifactId>TinyPinyin</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${springboot.version}</version>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>

                    <mainClass>com.yuelan.hermes.quanyi.QuanYiApplication</mainClass>
                    <layout>ZIP</layout>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>